"use client"
import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent, CardFooter } from "@/components/ui/card"
import { useState, useEffect, useRef, useMemo, useCallback, Suspense } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { BirthData, ZiweiDivinateRequest } from "@/types/user"
import { getZiweiResult } from "@/lib/divinate/ziwei"
import ZiweiChart from "@/components/ziwei/ziwei-chart"
import { ZiweiFortuneRequest } from "@/types/divinate_result"
import { setDivinateRequestFromBirthData } from "@/lib/astro-utils"
import Loading from "./loading"
import { FortuneTime, FortuneTimeRef } from "@/components/ziwei/fortune-time"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useAuth } from "@/hooks/use-auth"

// 带参数的组件，用Suspense包装
function ZiweiResultContent() {
  useA<PERSON>()
  const [astroData, setAstroData] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [ziweiDivinateRequest, setZiweiDivinateRequest] = useState<ZiweiDivinateRequest | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("本命") // 添加tab状态管理
  const [fortuneRequest, setFortuneRequest] = useState<ZiweiFortuneRequest | null>(null) // 运限请求状态
  const fortuneTimeRef = useRef<FortuneTimeRef>(null)
  const searchParams = useSearchParams()
  const router = useRouter()

  // 使用 useMemo 缓存解析后的URL参数，避免重复解析
  const parsedParams = useMemo(() => {
    const name = searchParams.get('name')
    const gender = searchParams.get('gender')
    const birthTime = searchParams.get('birthTime')
    const birthplace = searchParams.get('birthplace')
    const longitude = searchParams.get('longitude')
    const isLunar = searchParams.get('isLunar') === 'true'
    const useTrueSolarTime = searchParams.get('useTrueSolarTime') === 'true'
    const isDST = searchParams.get('isDST') === 'true'
    const isEarlyOrLateNight = searchParams.get('isEarlyOrLateNight') === 'true'
    const relationship = searchParams.get('relationship')
    const isSaveCaseDocument = searchParams.get('isSaveCaseDocument') === 'true'

    return {
      name, gender, birthTime, birthplace, longitude,
      isLunar, useTrueSolarTime, isDST, isEarlyOrLateNight, 
      relationship, isSaveCaseDocument
    }
  }, [searchParams])

  // 使用 useCallback 优化函数引用稳定性
  const loadDataAndCalculate = useCallback(async () => {
    try {
      const { 
        name, gender, birthTime, birthplace, longitude,
        isLunar, useTrueSolarTime, isDST, isEarlyOrLateNight, 
        relationship, isSaveCaseDocument 
      } = parsedParams

      // 验证必要参数
      if (!name || !gender || !birthTime || !birthplace || !relationship) {
        setError('缺少必要的出生信息参数')
        return
      }

      // 构建 BirthData 对象
      const parsedBirthData: BirthData = {
        name,
        gender: gender as 'male' | 'female',
        birthTime,
        birthplace,
        longitude: parseFloat(longitude || '0'),
        isLunar,
        useTrueSolarTime,
        isDST,
        isEarlyOrLateNight,
        relationship: relationship as any,
      }

      // 异步执行计算密集型操作，避免阻塞UI
      const ziweiDivinateRequest = await new Promise<ZiweiDivinateRequest>((resolve) => {
        // 使用 setTimeout 让出主线程，避免UI卡死
        setTimeout(() => {
          try {
            const request = setDivinateRequestFromBirthData(parsedBirthData)
            if (!request) {
              throw new Error('计算失败，请检查出生日期是否正确')
            }
            resolve(request)
          } catch (error) {
            throw error
          }
        }, 0)
      })

      setZiweiDivinateRequest(ziweiDivinateRequest)

      // 分批进行紫微斗数计算，避免一次性计算过多导致卡死
      const result = await new Promise<any>((resolve) => {
        setTimeout(() => {
          try {
            const astroResult = getZiweiResult(ziweiDivinateRequest)
            resolve(astroResult)
          } catch (error) {
            throw error
          }
        }, 0)
      })

      setAstroData(result)

      // 如果需要保存案例，异步处理
      if (isSaveCaseDocument) {
        console.log('需要保存案例文档')
        // TODO: 实现保存案例到数据库的逻辑
      }

    } catch (err) {
      console.error('紫微斗数计算失败:', err)
      setError(err instanceof Error ? err.message : '计算失败，请稍后再试')
    } finally {
      setIsLoading(false)
    }
  }, [parsedParams])

  useEffect(() => {
    // 添加性能监控
    const startTime = performance.now()
    
    loadDataAndCalculate().finally(() => {
      const endTime = performance.now()
      //console.log(`紫微斗数计算耗时: ${endTime - startTime}ms`)
    })
  }, [loadDataAndCalculate])

  // 返回测算页面
  const handleBackToCalculate = useCallback(() => {
    router.push('/ziwei/divinate')
  }, [router])

  // 切换到运限tab的处理函数
  const handleSwitchToFortuneTab = useCallback((tab: string) => {
    setActiveTab(tab)
  }, [])

  // 处理运限请求变化
  const handleFortuneRequestChange = useCallback((request: ZiweiFortuneRequest) => {
    setFortuneRequest(request)
  }, [])

  // 创建默认运限请求的辅助函数
  const createDefaultFortuneRequest = useCallback((type: string): ZiweiFortuneRequest => {
    return {
      type: type as '大限' | '流年' | '流月' | '流日' | '流时',
      date: new Date(),
      timeIndex: 6    //默认为午时
    }
  }, [])

  // 设置默认运限请求并更新FortuneTime组件
  const setDefaultFortuneRequest = useCallback((type: string) => {
    const defaultRequest = createDefaultFortuneRequest(type)
    setFortuneRequest(defaultRequest)
    // 设置FortuneTime组件的默认值
    fortuneTimeRef.current?.setDefaultValues(defaultRequest)
  }, [createDefaultFortuneRequest])

  // 处理tab变化 - 自动更新fortuneRequest.type
  const handleTabChange = useCallback((value: string) => {
    setActiveTab(value)
    // 当切换到运限tab时，更新或创建fortuneRequest
    if (value !== "本命") {
      if (fortuneRequest) {
        // 如果已有fortuneRequest，更新type
        setFortuneRequest({
          ...fortuneRequest,
          type: value as '大限' | '流年' | '流月' | '流日' | '流时'
        })
      } else {
        // 如果没有fortuneRequest，从FortuneTime组件获取当前选择状态来创建。目前的逻辑，正常不会走到这里
        const currentSelection = fortuneTimeRef.current?.getCurrentSelection()
        if (currentSelection) {
          try {
            const newFortuneRequest = currentSelection.createFortuneRequest(value as '大限' | '流年' | '流月' | '流日' | '流时', 
                                                                            currentSelection.selectedYear,
                                                                            currentSelection.selectedMonth,
                                                                            currentSelection.selectedDate?.getDate(),
                                                                            currentSelection.selectedTimeIndex)
            if (newFortuneRequest) {
              setFortuneRequest(newFortuneRequest)
            } else {
              // 如果createFortuneRequest返回null，说明没有有效选择，使用默认值
              setDefaultFortuneRequest(value)
            }
          } catch (error) {
            // 如果创建失败，使用默认值
            setDefaultFortuneRequest(value)
          }
        } else {
          // 如果无法获取当前选择，使用默认值
          setDefaultFortuneRequest(value)
        }
      }
    }
  }, [fortuneRequest, setDefaultFortuneRequest])

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Loading />
      </div>
    )
  }

  if (error || !ziweiDivinateRequest || !astroData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="shadow-lg mb-8">
          <CardContent>
            <div className="flex flex-col items-center justify-center h-[400px] text-center">
              <p className="text-red-500 mb-4">{error || '数据加载失败'}</p>
              <button
                onClick={handleBackToCalculate}
                className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
              >
                重新测算
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex flex-1 min-h-full w-full bg-background justify-center">
      <div className="container mx-0 md:mx-auto p-0 md:px-4 md:py-8 flex justify-center">
        <Card className="shadow-lg gap-0 mb-0 md:mb-8 w-full md:max-w-2xl py-4 md:py-6 bg-white rounded-none md:rounded-2xl">
          <CardHeader className="pb-0 gap-0">
            <CardTitle className="text-center text-xl md:text-2xl font-serif">紫微斗数星盘</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col gap-2 px-0 md:px-6">
            <div className="gap-2 md:gap-6">
              {/* 紫微斗数星盘 - 传入真实的 iztro 数据 */}
              <Tabs value={activeTab} onValueChange={handleTabChange} defaultValue="本命">
                <TabsContent value="本命" className="flex justify-center">
                  <ZiweiChart
                    name={ziweiDivinateRequest.name}
                    gender={ziweiDivinateRequest.gender}
                    birthTime={ziweiDivinateRequest.solarTime || ''}
                    trueSolarTime={ziweiDivinateRequest.trueSolarTime}
                    astroData={astroData}
                  />
                </TabsContent>
                <TabsContent value="大限" >
                  <ZiweiChart
                    name={ziweiDivinateRequest.name}
                    gender={ziweiDivinateRequest.gender}
                    birthTime={ziweiDivinateRequest.solarTime || ''}
                    trueSolarTime={ziweiDivinateRequest.trueSolarTime}
                    astroData={astroData}
                    fortuneRequest={fortuneRequest}
                  />
                </TabsContent>
                <TabsContent value="流年">
                  <ZiweiChart
                    name={ziweiDivinateRequest.name}
                    gender={ziweiDivinateRequest.gender}
                    birthTime={ziweiDivinateRequest.solarTime || ''}
                    trueSolarTime={ziweiDivinateRequest.trueSolarTime}
                    astroData={astroData}
                    fortuneRequest={fortuneRequest}
                  />
                </TabsContent>
                <TabsContent value="流月">
                  <ZiweiChart
                    name={ziweiDivinateRequest.name}
                    gender={ziweiDivinateRequest.gender}
                    birthTime={ziweiDivinateRequest.solarTime || ''}
                    trueSolarTime={ziweiDivinateRequest.trueSolarTime}
                    astroData={astroData}
                    fortuneRequest={fortuneRequest}
                  />
                </TabsContent>
                <TabsContent value="流日" >
                  <ZiweiChart
                    name={ziweiDivinateRequest.name}
                    gender={ziweiDivinateRequest.gender}
                    birthTime={ziweiDivinateRequest.solarTime || ''}
                    trueSolarTime={ziweiDivinateRequest.trueSolarTime}
                    astroData={astroData}
                    fortuneRequest={fortuneRequest}
                  />
                </TabsContent>
                <TabsContent value="流时" >
                  <ZiweiChart
                    name={ziweiDivinateRequest.name}
                    gender={ziweiDivinateRequest.gender}
                    birthTime={ziweiDivinateRequest.solarTime || ''}
                    trueSolarTime={ziweiDivinateRequest.trueSolarTime}
                    astroData={astroData}
                    fortuneRequest={fortuneRequest}
                  />
                </TabsContent>
                <div className="bg-white px-2 pd:mx-0">
                <TabsList className="bg-paper-secondary w-full justify-around" >
                  <TabsTrigger value="本命" className="w-1/6">本命盘</TabsTrigger>
                  <TabsTrigger value="大限" className="w-1/6">大限盘</TabsTrigger>
                  <TabsTrigger value="流年" className="w-1/6">流年盘</TabsTrigger>
                  <TabsTrigger value="流月" className="w-1/6">流月盘</TabsTrigger>
                  <TabsTrigger value="流日" className="w-1/6">流日盘</TabsTrigger>
                  <TabsTrigger value="流时" className="w-1/6">流时盘</TabsTrigger>
                </TabsList>
                </div>
              </Tabs>
            </div>
            <div className="flex justify-center">
              <FortuneTime 
                astroData={astroData} 
                onSelectChange={handleSwitchToFortuneTab}
                onFortuneRequestChange={handleFortuneRequestChange}
                ref={fortuneTimeRef}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default function ZiweiResultPage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto px-4 py-8">
        <Loading />
      </div>
    }>
      <ZiweiResultContent />
    </Suspense>
  )
}