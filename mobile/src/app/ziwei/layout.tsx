import dynamic from 'next/dynamic'
import MobileBottomNavigation from '@/components/layout/mobile/bottom-navigation'

// 动态导入以避免SSR问题
const TopTabs = dynamic(() => import('@/components/layout/mobile/top-tabs'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-64">
      <div className="flex items-center space-x-2">
        <div className="w-6 h-6 border-2 border-gray-300 border-t-primary rounded-full animate-spin"></div>
        <span className="text-gray-600">加载中...</span>
      </div>
    </div>
  )
})

export default function ZiweiLayout({ children }: { children: React.ReactNode }) {
    return (
        <div className="flex flex-col w-full min-h-full pb-bottom-nav overflow-y-scroll">
            <TopTabs currentCategory="ziwei" />
            <div className="flex flex-col w-full min-h-full overflow-y-scroll">
                {children}
            </div>
            <MobileBottomNavigation />
        </div>
    )
}