import '@/styles/globals.css'
import * as React from "react";
import type { Metadata } from 'next'
import { cn } from "@/lib/utils"
import { fontSans } from '@/app/fonts'
import { ClientProviders } from '@/components/providers/client-providers'
import HeadMeta from '../components/layout/head-meta'
import 'antd-mobile/es/global';
//import DebugPanel from '../components/debug-panel'
import type { Viewport } from 'next'


export const metadata: Metadata = {
  title: '玄学汇 - 命理爱好者的研究与学习平台',
  description: '专业的命理学教育平台，提供八字、紫微斗数、梅花易数在线测算、课程学习、经典书籍和案例解析',
  keywords: '命理学,玄学,算命,八字,紫微斗数,梅花易数,在线测算,命理学课程,古籍阅读',
}

 
export const viewport: Viewport = {
  viewportFit: 'cover',
  initialScale: 1,
  maximumScale: 1,
  minimumScale: 1,
  userScalable: false,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
 
  // mobile目录专门用于移动端，直接使用移动端布局
  return (
    <html lang="zh" className={fontSans.variable}>
      <head>
        <HeadMeta />
      </head>
      <body>
        <ClientProviders>
              <div className={cn(
                'min-h-screen h-full bg-background text-foreground',
                'overflow-x-hidden supports-[height:100dvh]:min-h-dvh '
              )}>
                <div className="flex flex-col min-h-screen relative "
                  style={{ height: '100dvh' }}>
                  <main className="flex safe-area-inset-top h-full w-full overflow-y-scroll pb-safe-bottom bg-background">
                    {/* 主内容区域 */}
                    {children}
                  </main>

                  {/* 调试面板 */}
                  {/* <DebugPanel /> */}
                </div>
              </div>
        </ClientProviders>
      </body>
    </html>
  )
} 