/** @type {import('next').NextConfig} */
const nextConfig = {
  // 静态导出配置，用于Capacitor打包
  output: 'export',
  
  // 禁用图片优化，因为静态导出不支持
  images: {
    unoptimized: true,
    domains: ['localhost', 'bazilab.com'],
  },
  
  webpack: (config) => {
    config.cache = true;
    return config 
  },

  // 关闭严格模式以避免某些兼容性问题
  reactStrictMode: false,
  
  // 禁用 trailing slash，保持URL简洁
  trailingSlash: true,
}

module.exports = nextConfig 